name: Publish

on:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'

jobs:
  publish:
    name: Publish
    runs-on: ubuntu-latest
    if: github.repository_owner == 'playcanvas'
    steps:
      - name: Check out code
        uses: actions/checkout@v5

      - name: Set up Node.js 18.x
        uses: actions/setup-node@v5
        with:
          node-version: 22.x
          cache: 'npm'
          registry-url: 'https://registry.npmjs.org/'

      - name: Install Dependencies
        run: npm ci

      - name: Build Splat Transform
        run: npm run build
        env:
          NODE_ENV: production

      - name: Run Publint
        run: npm run publint

      - name: Publish to npm
        run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
