name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v5

      - name: Set up Node.js 18.x
        uses: actions/setup-node@v5
        with:
          node-version: 22.x
          cache: 'npm'
          registry-url: 'https://registry.npmjs.org/'

      - name: Install Dependencies
        run: npm ci

      - name: Build Splat Transform
        run: npm run build

  lint:
    name: <PERSON>t
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v5

      - name: Set up Node.js 18.x
        uses: actions/setup-node@v5
        with:
          node-version: 22.x
          cache: 'npm'
          registry-url: 'https://registry.npmjs.org/'

      - name: Install Dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint
