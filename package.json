{"name": "@playcanvas/splat-transform", "version": "0.12.0", "author": "PlayCanvas<<EMAIL>>", "homepage": "https://playcanvas.com", "description": "CLI tool for 3D Gaussian splat format conversion and transformation", "keywords": ["3d-gaussian-splatting", "cli", "gaussian-splatting", "playcanvas", "supersplat", "typescript"], "license": "MIT", "bin": {"splat-transform": "bin/cli.mjs"}, "bugs": {"url": "https://github.com/playcanvas/splat-transform/issues"}, "repository": {"type": "git", "url": "git+https://github.com/playcanvas/splat-transform.git"}, "files": ["dist/", "lib/"], "devDependencies": {"@playcanvas/eslint-config": "2.1.0", "@rollup/plugin-json": "6.1.0", "@rollup/plugin-node-resolve": "16.0.2", "@rollup/plugin-typescript": "12.1.4", "@types/jsdom": "27.0.0", "@types/node": "24.7.0", "@typescript-eslint/eslint-plugin": "8.46.0", "@typescript-eslint/parser": "8.46.0", "eslint": "9.37.0", "eslint-import-resolver-typescript": "4.4.4", "playcanvas": "2.11.8", "publint": "0.3.14", "rollup": "4.52.4", "tslib": "2.8.1", "typescript": "5.9.3"}, "dependencies": {"@playcanvas/supersplat-viewer": "^1.6.2", "webgpu": "^0.3.8"}, "scripts": {"build": "rollup -c", "lint": "eslint src rollup.config.mjs eslint.config.mjs", "lint:fix": "eslint src rollup.config.mjs eslint.config.mjs --fix", "publint": "publint", "watch": "rollup -c -w"}, "engines": {"node": ">=18.0.0"}}