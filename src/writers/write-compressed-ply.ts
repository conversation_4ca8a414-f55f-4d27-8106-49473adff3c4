import { FileHandle } from 'node:fs/promises';

import { version } from '../../package.json';
import { CompressedChunk } from '../compressed-chunk';
import { DataTable } from '../data-table';
import { generateOrdering } from '../ordering';

const generatedByString = `Generated by splat-transform ${version}`;

const chunkProps = [
    'min_x', 'min_y', 'min_z',
    'max_x', 'max_y', 'max_z',
    'min_scale_x', 'min_scale_y', 'min_scale_z',
    'max_scale_x', 'max_scale_y', 'max_scale_z',
    'min_r', 'min_g', 'min_b',
    'max_r', 'max_g', 'max_b'
];

const vertexProps = [
    'packed_position',
    'packed_rotation',
    'packed_scale',
    'packed_color'
];

const shNames = new Array(45).fill('').map((_, i) => `f_rest_${i}`);

// Size of a chunk in the compressed PLY format (number of splats per chunk)
const CHUNK_SIZE = 256;

const writeCompressedPly = async (fileHandle: FileHandle, dataTable: DataTable) => {
    const shBands = { '9': 1, '24': 2, '-1': 3 }[shNames.findIndex(v => !dataTable.hasColumn(v))] ?? 0;
    const outputSHCoeffs = [0, 3, 8, 15][shBands];

    const numSplats = dataTable.numRows;
    const numChunks = Math.ceil(numSplats / CHUNK_SIZE);

    const shHeader = shBands ? [
        `element sh ${numSplats}`,
        new Array(outputSHCoeffs * 3).fill('').map((_, i) => `property uchar f_rest_${i}`)
    ].flat() : [];

    const headerText = [
        'ply',
        'format binary_little_endian 1.0',
        `comment ${generatedByString}`,
        `element chunk ${numChunks}`,
        chunkProps.map(p => `property float ${p}`),
        `element vertex ${numSplats}`,
        vertexProps.map(p => `property uint ${p}`),
        shHeader,
        'end_header\n'
    ].flat().join('\n');

    const header = (new TextEncoder()).encode(headerText);
    const chunkData = new Float32Array(numChunks * chunkProps.length);
    const splatIData = new Uint32Array(numSplats * vertexProps.length);
    const shData = new Uint8Array(numSplats * outputSHCoeffs * 3);

    // sort splats into some kind of order (morton order rn)
    const sortIndices = new Uint32Array(dataTable.numRows);
    for (let i = 0; i < sortIndices.length; ++i) {
        sortIndices[i] = i;
    }
    generateOrdering(dataTable, sortIndices);

    const row: any = {};

    const chunk = new CompressedChunk();

    for (let i = 0; i < numChunks; ++i) {
        const num = Math.min(numSplats, (i + 1) * CHUNK_SIZE) - i * CHUNK_SIZE;
        for (let j = 0; j < num; ++j) {
            const index = sortIndices[i * CHUNK_SIZE + j];

            // read splat data
            dataTable.getRow(index, row);

            // update chunk
            chunk.set(j, row);

            // quantize and write sh data
            let off = (i * CHUNK_SIZE + j) * outputSHCoeffs * 3;
            for (let k = 0; k < outputSHCoeffs * 3; ++k) {
                const nvalue = row[shNames[k]] / 8 + 0.5;
                shData[off++] = Math.max(0, Math.min(255, Math.trunc(nvalue * 256)));
            }
        }

        // repeat the last gaussian to fill the rest of the final chunk
        for (let j = num; j < CHUNK_SIZE; ++j) {
            chunk.set(j, row);
        }

        // pack the chunk
        chunk.pack();

        // store the float data
        chunkData.set(chunk.chunkData, i * 18);

        // write packed bits
        const offset = i * CHUNK_SIZE * 4;
        for (let j = 0; j < num; ++j) {
            splatIData[offset + j * 4 + 0] = chunk.position[j];
            splatIData[offset + j * 4 + 1] = chunk.rotation[j];
            splatIData[offset + j * 4 + 2] = chunk.scale[j];
            splatIData[offset + j * 4 + 3] = chunk.color[j];
        }
    }

    await fileHandle.write(header);
    await fileHandle.write(new Uint8Array(chunkData.buffer));
    await fileHandle.write(new Uint8Array(splatIData.buffer));
    await fileHandle.write(shData);
};

export { writeCompressedPly };
